#!/usr/bin/env python3
"""
测试 SceneLeapPlusDatasetCached 在不同 num_grasps 设置下的样本数变化
"""

import os
import sys
import torch
import time
import logging
from torch.utils.data import DataLoader

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datasets.sceneleapplus_cached import SceneLeapPlusDatasetCached

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_num_grasps_variations():
    """测试不同 num_grasps 设置下的数据集行为"""
    
    # 基础配置
    base_config = {
        "root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed",
        "succ_grasp_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect",
        "obj_root_dir": "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models",
        "mode": "camera_centric",
        "max_grasps_per_object": 1024,
        "mesh_scale": 0.1,
        "num_neg_prompts": 4,
        "enable_cropping": True,
        "max_points": 10000,
        "grasp_sampling_strategy": "farthest_point",
        "cache_version": "v1.0_plus",
        "cache_mode": "train"
    }

    # DataLoader 配置
    dataloader_config = {
        "batch_size": 2,
        "num_workers": 0  # 使用单进程避免复杂性
    }
    
    # 测试不同的 num_grasps 值
    num_grasps_values = [1, 4, 16, 64]
    
    print("=" * 80)
    print("测试 SceneLeapPlusDatasetCached 在不同 num_grasps 设置下的行为")
    print("=" * 80)
    
    results = {}
    
    for num_grasps in num_grasps_values:
        print(f"\n{'='*20} 测试 num_grasps = {num_grasps} {'='*20}")
        
        try:
            # 创建数据集实例
            config = base_config.copy()
            config["num_grasps"] = num_grasps
            
            start_time = time.time()
            dataset = SceneLeapPlusDatasetCached(**config)
            init_time = time.time() - start_time
            
            print(f"数据集初始化时间: {init_time:.2f}秒")
            print(f"数据集长度: {len(dataset)}")
            
            # 获取缓存信息
            cache_info = dataset.get_cache_info()
            print(f"缓存状态: {cache_info}")
            
            # 测试前几个样本
            sample_results = []
            test_indices = [0, 1, 2] if len(dataset) > 2 else list(range(len(dataset)))
            
            for idx in test_indices:
                try:
                    start_time = time.time()
                    sample = dataset[idx]
                    load_time = time.time() - start_time
                    
                    if 'error' in sample:
                        print(f"  样本 {idx}: 错误 - {sample['error']}")
                        sample_results.append({
                            'idx': idx,
                            'status': 'error',
                            'error': sample['error'],
                            'load_time': load_time
                        })
                    else:
                        # 检查关键字段的形状
                        hand_pose_shape = sample['hand_model_pose'].shape if 'hand_model_pose' in sample else None
                        se3_shape = sample['se3'].shape if 'se3' in sample else None
                        scene_pc_shape = sample['scene_pc'].shape if 'scene_pc' in sample else None
                        obj_verts_shape = sample['obj_verts'].shape if 'obj_verts' in sample else None
                        obj_faces_shape = sample['obj_faces'].shape if 'obj_faces' in sample else None
                        
                        print(f"  样本 {idx}: 成功加载 (耗时: {load_time:.4f}秒)")
                        print(f"    hand_model_pose: {hand_pose_shape}")
                        print(f"    se3: {se3_shape}")
                        print(f"    scene_pc: {scene_pc_shape}")
                        print(f"    obj_verts: {obj_verts_shape}")
                        print(f"    obj_faces: {obj_faces_shape}")
                        print(f"    positive_prompt: {sample.get('positive_prompt', 'N/A')}")
                        print(f"    negative_prompts数量: {len(sample.get('negative_prompts', []))}")
                        
                        sample_results.append({
                            'idx': idx,
                            'status': 'success',
                            'hand_pose_shape': hand_pose_shape,
                            'se3_shape': se3_shape,
                            'scene_pc_shape': scene_pc_shape,
                            'obj_verts_shape': obj_verts_shape,
                            'obj_faces_shape': obj_faces_shape,
                            'load_time': load_time,
                            'positive_prompt': sample.get('positive_prompt', 'N/A'),
                            'num_negative_prompts': len(sample.get('negative_prompts', []))
                        })
                        
                except Exception as e:
                    print(f"  样本 {idx}: 异常 - {str(e)}")
                    sample_results.append({
                        'idx': idx,
                        'status': 'exception',
                        'error': str(e),
                        'load_time': 0
                    })
            
            # 测试 DataLoader
            print(f"\n测试 DataLoader (batch_size={dataloader_config['batch_size']}):")
            try:
                dataloader = DataLoader(
                    dataset,
                    batch_size=dataloader_config['batch_size'],
                    shuffle=False,
                    num_workers=dataloader_config['num_workers'],
                    collate_fn=SceneLeapPlusDatasetCached.collate_fn
                )
                
                # 测试一个批次
                start_time = time.time()
                batch = next(iter(dataloader))
                batch_time = time.time() - start_time
                
                if 'error' in batch:
                    print(f"  批次加载错误: {batch['error']}")
                else:
                    batch_hand_pose_shape = batch['hand_model_pose'].shape if 'hand_model_pose' in batch else None
                    batch_se3_shape = batch['se3'].shape if 'se3' in batch else None
                    batch_scene_pc_shape = batch['scene_pc'].shape if 'scene_pc' in batch else None
                    
                    print(f"  批次加载成功 (耗时: {batch_time:.4f}秒)")
                    print(f"    批次 hand_model_pose: {batch_hand_pose_shape}")
                    print(f"    批次 se3: {batch_se3_shape}")
                    print(f"    批次 scene_pc: {batch_scene_pc_shape}")
                    
            except Exception as e:
                print(f"  DataLoader 测试失败: {str(e)}")
            
            # 保存结果
            results[num_grasps] = {
                'dataset_length': len(dataset),
                'init_time': init_time,
                'cache_info': cache_info,
                'sample_results': sample_results
            }
            
            # 清理资源
            if hasattr(dataset, '_cleanup'):
                dataset._cleanup()
            del dataset
            
        except Exception as e:
            print(f"测试 num_grasps={num_grasps} 时发生错误: {str(e)}")
            results[num_grasps] = {
                'error': str(e)
            }
    
    # 汇总结果
    print(f"\n{'='*20} 测试结果汇总 {'='*20}")
    for num_grasps, result in results.items():
        print(f"\nnum_grasps = {num_grasps}:")
        if 'error' in result:
            print(f"  错误: {result['error']}")
        else:
            print(f"  数据集长度: {result['dataset_length']}")
            print(f"  初始化时间: {result['init_time']:.2f}秒")
            print(f"  缓存状态: {result['cache_info'].get('cache_loaded', False)}")
            
            # 统计样本结果
            success_count = sum(1 for r in result['sample_results'] if r['status'] == 'success')
            error_count = sum(1 for r in result['sample_results'] if r['status'] == 'error')
            exception_count = sum(1 for r in result['sample_results'] if r['status'] == 'exception')
            
            print(f"  样本测试: 成功 {success_count}, 错误 {error_count}, 异常 {exception_count}")
            
            # 显示成功样本的形状信息
            for r in result['sample_results']:
                if r['status'] == 'success':
                    print(f"    样本 {r['idx']}: hand_pose {r['hand_pose_shape']}, se3 {r['se3_shape']}")
    
    return results

if __name__ == "__main__":
    print("开始测试 SceneLeapPlusDatasetCached 的 num_grasps 参数...")
    results = test_num_grasps_variations()
    print("\n测试完成!")
